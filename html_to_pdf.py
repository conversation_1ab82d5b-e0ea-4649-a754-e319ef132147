import asyncio
import os
import sys
import argparse
from pathlib import Path
import json

try:
    from playwright.async_api import async_playwright
except ImportError:
    print("❌ 请先安装 playwright: pip install playwright")
    print("   然后运行: playwright install chromium")
    sys.exit(1)

class HTMLToPDFConverter:
    """使用Playwright的HTML转PDF转换器"""
    
    def __init__(self):
        self.default_options = {
            'format': 'A4',
            'margin': {
                'top': '20mm',
                'right': '20mm',
                'bottom': '20mm',
                'left': '20mm'
            },
            'print_background': True,
            'prefer_css_page_size': True,
            'display_header_footer': False
        }
    
    async def convert_file(self, html_file_path, output_pdf_path=None, options=None):
        """
        将HTML文件转换为PDF
        
        Args:
            html_file_path (str): HTML文件路径
            output_pdf_path (str, optional): 输出PDF路径
            options (dict, optional): PDF生成选项
        
        Returns:
            str: 生成的PDF文件路径
        """
        # 检查输入文件
        if not os.path.exists(html_file_path):
            raise FileNotFoundError(f"HTML文件不存在: {html_file_path}")
        
        # 设置输出路径
        if output_pdf_path is None:
            html_path = Path(html_file_path)
            output_pdf_path = html_path.with_suffix('.pdf')
        
        # 确保输出目录存在
        output_dir = Path(output_pdf_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 合并选项
        final_options = self.default_options.copy()
        if options:
            final_options.update(options)
        
        print(f"🔄 开始转换: {html_file_path} -> {output_pdf_path}")
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 加载HTML文件
                file_url = f"file://{os.path.abspath(html_file_path)}"
                await page.goto(file_url, wait_until='networkidle', timeout=30000)
                
                # 等待页面完全加载
                await page.wait_for_load_state('networkidle')
                
                # 生成PDF
                await page.pdf(path=output_pdf_path, **final_options)
                
                await browser.close()
                print(f"✅ 转换成功: {output_pdf_path}")
                return str(output_pdf_path)
                
            except Exception as e:
                print(f"❌ 转换失败: {str(e)}")
                if 'browser' in locals():
                    await browser.close()
                raise
    
    async def convert_string(self, html_string, output_pdf_path, options=None):
        """
        将HTML字符串转换为PDF
        
        Args:
            html_string (str): HTML字符串
            output_pdf_path (str): 输出PDF路径
            options (dict, optional): PDF生成选项
        
        Returns:
            str: 生成的PDF文件路径
        """
        # 确保输出目录存在
        output_dir = Path(output_pdf_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 合并选项
        final_options = self.default_options.copy()
        if options:
            final_options.update(options)
        
        print(f"🔄 开始转换HTML字符串 -> {output_pdf_path}")
        
        async with async_playwright() as p:
            try:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置HTML内容
                await page.set_content(html_string, wait_until='networkidle', timeout=30000)
                
                # 等待页面完全加载
                await page.wait_for_load_state('networkidle')
                
                # 生成PDF
                await page.pdf(path=output_pdf_path, **final_options)
                
                await browser.close()
                print(f"✅ 转换成功: {output_pdf_path}")
                return str(output_pdf_path)
                
            except Exception as e:
                print(f"❌ 转换失败: {str(e)}")
                if 'browser' in locals():
                    await browser.close()
                raise
    
    async def batch_convert(self, html_files, output_dir=None, options=None):
        """
        批量转换HTML文件
        
        Args:
            html_files (list): HTML文件路径列表
            output_dir (str, optional): 输出目录
            options (dict, optional): PDF生成选项
        
        Returns:
            list: 生成的PDF文件路径列表
        """
        results = []
        
        for html_file in html_files:
            try:
                if output_dir:
                    html_name = Path(html_file).stem
                    output_path = Path(output_dir) / f"{html_name}.pdf"
                else:
                    output_path = None
                
                result = await self.convert_file(html_file, str(output_path), options)
                results.append(result)
                
            except Exception as e:
                print(f"❌ 批量转换失败 {html_file}: {str(e)}")
                results.append(None)
        
        return results

# 同步包装器
def convert_html_to_pdf(html_file_path, output_pdf_path=None, options=None):
    """同步版本的转换函数"""
    converter = HTMLToPDFConverter()
    return asyncio.run(converter.convert_file(html_file_path, output_pdf_path, options))

def convert_html_string_to_pdf(html_string, output_pdf_path, options=None):
    """同步版本的HTML字符串转换函数"""
    converter = HTMLToPDFConverter()
    return asyncio.run(converter.convert_string(html_string, output_pdf_path, options))

async def main():
    """异步命令行入口"""
    parser = argparse.ArgumentParser(
        description='HTML转PDF工具 (Playwright版)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python html_to_pdf.py input.html
  python html_to_pdf.py input.html -o output.pdf
  python html_to_pdf.py input.html --format A3 --margin 10mm
  python html_to_pdf.py *.html --batch --output-dir ./pdfs
        """
    )
    
    parser.add_argument('input', nargs='+', help='输入的HTML文件路径(支持通配符)')
    parser.add_argument('-o', '--output', help='输出的PDF文件路径')
    parser.add_argument('--output-dir', help='批量转换时的输出目录')
    parser.add_argument('--batch', action='store_true', help='批量转换模式')
    parser.add_argument('--format', default='A4', 
                       choices=['A4', 'A3', 'A5', 'Letter', 'Legal', 'Tabloid'],
                       help='页面格式 (默认: A4)')
    parser.add_argument('--margin', default='20mm', help='页边距 (默认: 20mm)')
    parser.add_argument('--landscape', action='store_true', help='横向布局')
    parser.add_argument('--no-background', action='store_true', help='不打印背景')
    parser.add_argument('--header-footer', action='store_true', help='显示页眉页脚')
    parser.add_argument('--scale', type=float, default=1.0, help='缩放比例 (0.1-2.0)')
    
    args = parser.parse_args()
    
    # 创建转换器
    converter = HTMLToPDFConverter()
    
    # 构建自定义选项
    custom_options = {
        'format': args.format,
        'margin': {
            'top': args.margin,
            'right': args.margin,
            'bottom': args.margin,
            'left': args.margin
        },
        'landscape': args.landscape,
        'print_background': not args.no_background,
        'display_header_footer': args.header_footer,
        'scale': max(0.1, min(2.0, args.scale))  # 限制缩放范围
    }
    
    try:
        if args.batch or len(args.input) > 1:
            # 批量转换模式
            print(f"📁 批量转换模式，共 {len(args.input)} 个文件")
            results = await converter.batch_convert(
                args.input,
                args.output_dir,
                custom_options
            )
            
            success_count = sum(1 for r in results if r is not None)
            print(f"\n📊 转换完成: {success_count}/{len(args.input)} 个文件成功")
            
        else:
            # 单文件转换模式
            output_path = await converter.convert_file(
                args.input[0],
                args.output,
                custom_options
            )
            print(f"📄 PDF文件已生成: {output_path}")
            
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)

# 使用示例和测试函数
def create_test_html():
    """创建测试HTML文件"""
    test_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文档</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .highlight {
            background-color: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .info-box {
            background-color: #ecf0f1;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #34495e;
            color: white;
        }
    </style>
</head>
<body>
    <h1>HTML转PDF测试文档</h1>
    
    <h2>功能特性</h2>
    <ul>
        <li>支持<span class="highlight">中文字体</span>渲染</li>
        <li>完整的CSS样式支持</li>
        <li>表格和布局保持</li>
        <li>背景色和边框正常显示</li>
    </ul>
    
    <div class="info-box">
        <strong>提示:</strong> 这是一个信息框，用于测试CSS样式在PDF中的渲染效果。
    </div>
    
    <h2>数据表格</h2>
    <table>
        <thead>
            <tr>
                <th>项目</th>
                <th>状态</th>
                <th>进度</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>HTML解析</td>
                <td>完成</td>
                <td>100%</td>
            </tr>
            <tr>
                <td>CSS渲染</td>
                <td>完成</td>
                <td>100%</td>
            </tr>
            <tr>
                <td>PDF生成</td>
                <td>进行中</td>
                <td>90%</td>
            </tr>
        </tbody>
    </table>
    
    <h2>总结</h2>
    <p>这个HTML转PDF工具使用Playwright引擎，能够准确渲染现代Web页面，包括复杂的CSS样式、中文字体和各种布局元素。</p>
</body>
</html>"""
    
    with open('/home/<USER>/test.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 测试HTML文件已创建: test.html")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 命令行模式
        asyncio.run(main())
    else:
        # 交互模式 - 显示帮助信息
        print("🔧 HTML转PDF工具 (Playwright版)")
        print("=" * 50)
        print()
        print("📦 安装依赖:")
        print("   pip install playwright")
        print("   playwright install chromium")
        print()
        print("💡 使用方法:")
        print("   1. 单文件转换:")
        print("      python html_to_pdf.py input.html")
        print("      python html_to_pdf.py input.html -o output.pdf")
        print()
        print("   2. 批量转换:")
        print("      python html_to_pdf.py *.html --batch --output-dir ./pdfs")
        print()
        print("   3. 自定义选项:")
        print("      python html_to_pdf.py input.html --format A3 --margin 10mm --landscape")
        print()
        print("   4. Python代码调用:")
        print("      from html_to_pdf import convert_html_to_pdf")
        print("      convert_html_to_pdf('input.html', 'output.pdf')")
        print()
        print("🧪 创建测试文件:")
        create_test_html()