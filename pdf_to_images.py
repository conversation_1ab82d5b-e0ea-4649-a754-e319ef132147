#!/usr/bin/env python3
"""
PDF 转图片工具
将 PDF 文件按页数转换为图片文件

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import argparse
from pathlib import Path
from pdf2image import convert_from_path
from PIL import Image


class PDFToImageConverter:
    """PDF 转图片转换器"""
    
    def __init__(self):
        self.supported_formats = ['PNG', 'JPEG', 'TIFF', 'BMP']
    
    def convert_pdf_to_images(self, pdf_path, output_dir=None, format='PNG', 
                             dpi=200, quality=95, prefix='page'):
        """
        将 PDF 转换为图片
        
        参数:
            pdf_path (str): PDF 文件路径
            output_dir (str): 输出目录，默认为 PDF 文件同目录
            format (str): 输出图片格式 (PNG, JPEG, TIFF, BMP)
            dpi (int): 图片分辨率，默认 200
            quality (int): JPEG 质量 (1-100)，默认 95
            prefix (str): 文件名前缀，默认 'page'
        
        返回:
            list: 生成的图片文件路径列表
        """
        
        # 验证输入文件
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF 文件不存在: {pdf_path}")
        
        if not pdf_path.lower().endswith('.pdf'):
            raise ValueError("输入文件必须是 PDF 格式")
        
        # 验证输出格式
        format = format.upper()
        if format not in self.supported_formats:
            raise ValueError(f"不支持的格式: {format}. 支持的格式: {', '.join(self.supported_formats)}")
        
        # 设置输出目录
        if output_dir is None:
            output_dir = os.path.dirname(pdf_path)
            if not output_dir:
                output_dir = '.'
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取 PDF 文件名（不含扩展名）
        pdf_name = Path(pdf_path).stem
        
        print(f"开始转换 PDF: {pdf_path}")
        print(f"输出目录: {output_dir}")
        print(f"输出格式: {format}")
        print(f"分辨率: {dpi} DPI")
        
        try:
            # 转换 PDF 为图片
            images = convert_from_path(pdf_path, dpi=dpi)
            
            print(f"PDF 共有 {len(images)} 页")
            
            output_files = []
            
            # 保存每一页为图片
            for i, image in enumerate(images, 1):
                # 生成文件名
                if format == 'JPEG':
                    extension = 'jpg'
                else:
                    extension = format.lower()
                
                filename = f"{prefix}_{i:03d}.{extension}"
                output_path = os.path.join(output_dir, filename)
                
                # 保存图片
                if format == 'JPEG':
                    # JPEG 不支持透明度，需要转换为 RGB
                    if image.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', image.size, (255, 255, 255))
                        if image.mode == 'P':
                            image = image.convert('RGBA')
                        background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                        image = background
                    image.save(output_path, format, quality=quality, optimize=True)
                else:
                    image.save(output_path, format)
                
                output_files.append(output_path)
                print(f"已保存第 {i} 页: {filename}")
            
            print(f"\n转换完成！共生成 {len(output_files)} 个图片文件")
            return output_files
            
        except Exception as e:
            raise RuntimeError(f"转换过程中出现错误: {str(e)}")
    
    def get_pdf_info(self, pdf_path):
        """
        获取 PDF 文件信息
        
        参数:
            pdf_path (str): PDF 文件路径
        
        返回:
            dict: PDF 信息
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF 文件不存在: {pdf_path}")
        
        try:
            # 只转换第一页来获取信息
            images = convert_from_path(pdf_path, first_page=1, last_page=1)
            if images:
                first_page = images[0]
                
                # 获取总页数（需要重新转换来计算）
                all_images = convert_from_path(pdf_path)
                total_pages = len(all_images)
                
                return {
                    'file_path': pdf_path,
                    'file_size': os.path.getsize(pdf_path),
                    'total_pages': total_pages,
                    'first_page_size': first_page.size,
                    'first_page_mode': first_page.mode
                }
        except Exception as e:
            raise RuntimeError(f"获取 PDF 信息时出现错误: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='将 PDF 文件按页数转换为图片',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python pdf_to_images.py input.pdf                    # 转换为 PNG 格式
  python pdf_to_images.py input.pdf -f JPEG            # 转换为 JPEG 格式
  python pdf_to_images.py input.pdf -o ./images        # 指定输出目录
  python pdf_to_images.py input.pdf -d 300 -q 90       # 设置分辨率和质量
  python pdf_to_images.py input.pdf --info             # 只显示 PDF 信息
        """
    )
    
    parser.add_argument('pdf_file', help='输入的 PDF 文件路径')
    parser.add_argument('-o', '--output', help='输出目录（默认为 PDF 文件同目录）')
    parser.add_argument('-f', '--format', choices=['PNG', 'JPEG', 'TIFF', 'BMP'], 
                       default='PNG', help='输出图片格式（默认: PNG）')
    parser.add_argument('-d', '--dpi', type=int, default=200, 
                       help='图片分辨率 DPI（默认: 200）')
    parser.add_argument('-q', '--quality', type=int, default=95, 
                       help='JPEG 质量 1-100（默认: 95）')
    parser.add_argument('-p', '--prefix', default='page', 
                       help='输出文件名前缀（默认: page）')
    parser.add_argument('--info', action='store_true', 
                       help='只显示 PDF 文件信息，不进行转换')
    
    args = parser.parse_args()
    
    # 验证参数
    if args.quality < 1 or args.quality > 100:
        print("错误: JPEG 质量必须在 1-100 之间")
        sys.exit(1)
    
    if args.dpi < 50 or args.dpi > 600:
        print("警告: DPI 建议在 50-600 之间，过低会影响质量，过高会增加文件大小")
    
    converter = PDFToImageConverter()
    
    try:
        if args.info:
            # 只显示 PDF 信息
            info = converter.get_pdf_info(args.pdf_file)
            print(f"\nPDF 文件信息:")
            print(f"文件路径: {info['file_path']}")
            print(f"文件大小: {info['file_size'] / 1024 / 1024:.2f} MB")
            print(f"总页数: {info['total_pages']}")
            print(f"首页尺寸: {info['first_page_size'][0]} x {info['first_page_size'][1]} 像素")
            print(f"颜色模式: {info['first_page_mode']}")
        else:
            # 执行转换
            output_files = converter.convert_pdf_to_images(
                pdf_path=args.pdf_file,
                output_dir=args.output,
                format=args.format,
                dpi=args.dpi,
                quality=args.quality,
                prefix=args.prefix
            )
            
            print(f"\n生成的文件:")
            for file_path in output_files:
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  {file_path} ({file_size:.1f} KB)")
    
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

